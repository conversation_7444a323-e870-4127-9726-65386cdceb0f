<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可视化看板与预警 - 基于零售站点前置仓的巡检系统</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/visual_dashboard.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    <!-- 标题将由menu.js动态设置 -->
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">可视化看板与预警</h2>
                </div>
                <div class="top-bar-right">
                    <div class="dashboard-controls">
                        <select id="timeRangeFilter" class="form-select">
                            <option value="realtime">实时</option>
                            <option value="1h">最近1小时</option>
                            <option value="6h">最近6小时</option>
                            <option value="24h">最近24小时</option>
                            <option value="7d">最近7天</option>
                        </select>
                        <button class="btn btn-refresh" onclick="refreshDashboard()">🔄 刷新</button>
                        <button class="btn btn-export" onclick="exportDashboard()">📊 导出</button>
                    </div>
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">

        <!-- 运营健康度驾驶舱 -->
        <div class="row health-cockpit">
            <div class="col-lg-8">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>🎯 运营健康度驾驶舱</h3>
                        <div class="health-score-display">
                            <div class="score-circle">
                                <canvas id="healthScoreGauge" width="120" height="120"></canvas>
                                <div class="score-text">
                                    <span id="overallHealthScore">85.2</span>
                                    <small>健康度</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="health-categories">
                            <div class="category-item">
                                <div class="category-icon">🔧</div>
                                <div class="category-info">
                                    <h4>设备状态</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill equipment" id="equipmentProgress"></div>
                                    </div>
                                    <span class="score" id="equipmentScore">82.5%</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">🌡️</div>
                                <div class="category-info">
                                    <h4>环境参数</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill environment" id="environmentProgress"></div>
                                    </div>
                                    <span class="score" id="environmentScore">88.7%</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">👥</div>
                                <div class="category-info">
                                    <h4>人员效率</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill personnel" id="personnelProgress"></div>
                                    </div>
                                    <span class="score" id="personnelScore">91.2%</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">🛡️</div>
                                <div class="category-info">
                                    <h4>安全指数</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill safety" id="safetyProgress"></div>
                                    </div>
                                    <span class="score" id="safetyScore">79.8%</span>
                                </div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">⚡</div>
                                <div class="category-info">
                                    <h4>运营效率</h4>
                                    <div class="progress-bar">
                                        <div class="progress-fill efficiency" id="efficiencyProgress"></div>
                                    </div>
                                    <span class="score" id="efficiencyScore">86.3%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>📊 健康度趋势</h3>
                    </div>
                    <div class="panel-body">
                        <canvas id="healthTrendChart" width="300" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI指标面板 -->
        <div class="row kpi-panels">
            <div class="col-lg-12">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>📈 关键绩效指标(KPI)</h3>
                        <div class="panel-controls">
                            <button class="btn btn-sm btn-primary" onclick="customizeKPI()">自定义指标</button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="kpi-grid" id="kpiGrid">
                            <!-- KPI指标卡片将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备状态与故障预警 -->
        <div class="row equipment-panels">
            <div class="col-lg-6">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>🔧 设备状态分布</h3>
                    </div>
                    <div class="panel-body">
                        <div class="equipment-overview">
                            <div class="equipment-chart">
                                <canvas id="equipmentStatusChart" width="250" height="250"></canvas>
                            </div>
                            <div class="equipment-stats" id="equipmentStats">
                                <!-- 设备统计信息 -->
                            </div>
                        </div>
                        <div class="equipment-categories" id="equipmentCategories">
                            <!-- 设备分类统计 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>⚠️ 故障预警预测</h3>
                        <div class="panel-controls">
                            <select id="predictionTimeframe" class="form-select">
                                <option value="7d">未来7天</option>
                                <option value="15d">未来15天</option>
                                <option value="30d">未来30天</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="prediction-list" id="predictionList">
                            <!-- 故障预测列表将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 运营数据面板 -->
        <div class="row operation-panels">
            <div class="col-lg-4">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>⚡ 能耗监控</h3>
                    </div>
                    <div class="panel-body">
                        <div class="energy-overview">
                            <div class="energy-current">
                                <h2 id="currentEnergy">2850.5</h2>
                                <span>kWh</span>
                                <small>今日消耗</small>
                            </div>
                            <div class="energy-comparison">
                                <span class="comparison-label">与昨日相比</span>
                                <span class="comparison-value" id="energyComparison">+3.1%</span>
                            </div>
                        </div>
                        <canvas id="energyTrendChart" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>📊 运营效率</h3>
                    </div>
                    <div class="panel-body">
                        <div class="efficiency-overview">
                            <div class="efficiency-current">
                                <h2 id="currentEfficiency">92.8</h2>
                                <span>%</span>
                                <small>当前效率</small>
                            </div>
                            <div class="efficiency-target">
                                <span>目标: <strong id="efficiencyTarget">95.0%</strong></span>
                            </div>
                        </div>
                        <div class="efficiency-areas" id="efficiencyAreas">
                            <!-- 各区域效率 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>📦 吞吐量监控</h3>
                    </div>
                    <div class="panel-body">
                        <div class="throughput-overview">
                            <div class="throughput-current">
                                <h2 id="currentThroughput">1850</h2>
                                <span>件</span>
                                <small>今日吞吐</small>
                            </div>
                            <div class="throughput-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="throughputProgress"></div>
                                </div>
                                <span>目标完成度: <span id="throughputPercentage">92.5%</span></span>
                            </div>
                        </div>
                        <canvas id="throughputChart" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 温度热力图和实时预警 -->
        <div class="row monitoring-panels">
            <div class="col-lg-8">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>🌡️ 温度分布热力图</h3>
                        <div class="panel-controls">
                            <select id="temperatureView" class="form-select">
                                <option value="current">当前温度</option>
                                <option value="average">平均温度</option>
                                <option value="variance">温度变化</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="temperature-heatmap" id="temperatureHeatmap">
                            <!-- 温度热力图将通过JS动态生成 -->
                        </div>
                        <div class="temperature-legend">
                            <span class="legend-item normal">正常 (-20°C ~ 4°C)</span>
                            <span class="legend-item warning">警告 (4°C ~ 8°C)</span>
                            <span class="legend-item error">异常 (>8°C)</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="dashboard-panel">
                    <div class="panel-header">
                        <h3>🚨 实时预警</h3>
                        <div class="alert-summary">
                            <span class="alert-count critical" id="criticalAlertCount">3</span>
                            <span class="alert-count warning" id="warningAlertCount">8</span>
                            <span class="alert-count info" id="infoAlertCount">5</span>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="alert-list" id="alertList">
                            <!-- 预警列表将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时统计信息底部面板 -->
        <div class="row stats-bottom">
            <div class="col-lg-12">
                <div class="dashboard-panel stats-panel">
                    <div class="panel-body">
                        <div class="real-time-stats" id="realTimeStats">
                            <div class="stat-item">
                                <span class="stat-label">活跃设备</span>
                                <span class="stat-value" id="activeDevicesCount">148</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总预警数</span>
                                <span class="stat-value" id="totalAlertsCount">28</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">能耗使用</span>
                                <span class="stat-value" id="energyUsageValue">2850.5 kWh</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">系统负载</span>
                                <span class="stat-value" id="systemLoadValue">76.3%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">网络延迟</span>
                                <span class="stat-value" id="networkLatencyValue">15.2ms</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">数据处理率</span>
                                <span class="stat-value" id="dataProcessingValue">1250.8/s</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">最后更新</span>
                                <span class="stat-value" id="lastUpdateValue">14:30:25</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <!-- 预警详情模态框 -->
    <div class="modal fade" id="alertDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">预警详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="alertDetailContent">
                    <!-- 预警详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning" onclick="acknowledgeAlert()">确认预警</button>
                    <button type="button" class="btn btn-primary" onclick="resolveAlert()">解决预警</button>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI自定义模态框 -->
    <div class="modal fade" id="kpiCustomModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">自定义KPI指标</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="kpi-options" id="kpiOptions">
                        <!-- KPI选项将通过JS动态生成 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveKpiCustomization()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/chart.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/visual_dashboard.js"></script>
</body>
</html>