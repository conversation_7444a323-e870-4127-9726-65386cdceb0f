<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多维度实时监控</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/realtime_monitoring.css">
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <div class="header-left">
                <button class="btn-back" onclick="window.location.href='home.html'">
                    <i class="fa fa-arrow-left"></i> 返回
                </button>
                <h1>
                    <span class="logo-icon">📊</span>
                    <!-- 标题将由menu.js动态设置 -->
                </h1>
            </div>
            <div class="header-right">
                <div class="real-time-info">
                    <span class="status-indicator active"></span>
                    <span>实时监控中</span>
                    <span class="last-update">最后更新: <span id="lastUpdateTime">--</span></span>
                </div>
            </div>
        </div>

        <!-- 统计概览卡片 -->
        <div class="row stats-overview">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon devices">📱</div>
                    <div class="stat-info">
                        <h3 id="totalDevices">--</h3>
                        <p>设备总数</p>
                        <div class="stat-details">
                            <span class="normal">正常: <span id="normalDevices">--</span></span>
                            <span class="warning">警告: <span id="warningDevices">--</span></span>
                            <span class="error">故障: <span id="errorDevices">--</span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon personnel">👥</div>
                    <div class="stat-info">
                        <h3 id="activePersonnel">--</h3>
                        <p>在线人员</p>
                        <div class="stat-details">
                            <span class="total">总计: <span id="totalPersonnel">--</span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon alarms">🚨</div>
                    <div class="stat-info">
                        <h3 id="totalAlarms">--</h3>
                        <p>报警数量</p>
                        <div class="stat-details">
                            <span class="critical">严重: <span id="criticalAlarms">--</span></span>
                            <span class="warning">警告: <span id="warningAlarms">--</span></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon health">💚</div>
                    <div class="stat-info">
                        <h3 id="systemHealth">--%</h3>
                        <p>系统健康度</p>
                        <div class="stat-details">
                            <span class="efficiency">运营效率: <span id="operationEfficiency">--%</span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要监控面板 -->
        <div class="row monitoring-panels">
            <!-- 设备状态监控 -->
            <div class="col-lg-6">
                <div class="monitor-panel">
                    <div class="panel-header">
                        <h3>🔧 设备状态监控</h3>
                        <div class="panel-controls">
                            <select id="deviceTypeFilter" class="form-select">
                                <option value="all">全部设备</option>
                                <option value="temperature">温度传感器</option>
                                <option value="humidity">湿度传感器</option>
                                <option value="equipment">设备</option>
                                <option value="ventilation">通风系统</option>
                                <option value="lighting">照明系统</option>
                                <option value="access">门禁系统</option>
                                <option value="camera">监控摄像头</option>
                            </select>
                            <button class="btn btn-refresh" onclick="refreshDeviceData()">🔄</button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="device-list" id="deviceList">
                            <!-- 设备列表将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 环境参数监控 -->
            <div class="col-lg-6">
                <div class="monitor-panel">
                    <div class="panel-header">
                        <h3>🌡️ 环境参数监控</h3>
                        <div class="panel-controls">
                            <button class="btn btn-refresh" onclick="refreshEnvironmentData()">🔄</button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="environment-grid" id="environmentGrid">
                            <!-- 环境数据将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 人员动线和报警管理 -->
        <div class="row tracking-panels">
            <!-- 人员动线追踪 -->
            <div class="col-lg-8">
                <div class="monitor-panel">
                    <div class="panel-header">
                        <h3>👤 人员动线追踪</h3>
                        <div class="panel-controls">
                            <select id="personnelFilter" class="form-select">
                                <option value="all">全部人员</option>
                                <option value="active">在线人员</option>
                                <option value="offline">离线人员</option>
                            </select>
                            <button class="btn btn-refresh" onclick="refreshPersonnelData()">🔄</button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="personnel-map">
                            <div class="map-container" id="personnelMap">
                                <!-- 人员位置图将通过JS动态生成 -->
                            </div>
                        </div>
                        <div class="personnel-list" id="personnelList">
                            <!-- 人员列表将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时报警管理 -->
            <div class="col-lg-4">
                <div class="monitor-panel">
                    <div class="panel-header">
                        <h3>⚠️ 实时报警</h3>
                        <div class="panel-controls">
                            <select id="alarmLevelFilter" class="form-select">
                                <option value="all">全部级别</option>
                                <option value="critical">严重</option>
                                <option value="warning">警告</option>
                                <option value="info">信息</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="alarm-list" id="alarmList">
                            <!-- 报警列表将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时图表监控 -->
        <div class="row chart-panels">
            <div class="col-lg-12">
                <div class="monitor-panel">
                    <div class="panel-header">
                        <h3>📈 实时数据趋势</h3>
                        <div class="panel-controls">
                            <select id="chartDataType" class="form-select">
                                <option value="temperature">温度趋势</option>
                                <option value="humidity">湿度趋势</option>
                                <option value="energy">能耗趋势</option>
                                <option value="efficiency">效率趋势</option>
                            </select>
                            <select id="chartTimeRange" class="form-select">
                                <option value="1h">最近1小时</option>
                                <option value="6h">最近6小时</option>
                                <option value="24h">最近24小时</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-body">
                        <canvas id="realtimeChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详情模态框 -->
    <div class="modal fade" id="deviceDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设备详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="deviceDetailContent">
                    <!-- 设备详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="exportDeviceReport()">导出报告</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 报警处理模态框 -->
    <div class="modal fade" id="alarmHandleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">处理报警</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>处理方式:</label>
                        <select id="handleAction" class="form-control">
                            <option value="confirm">确认处理</option>
                            <option value="ignore">暂时忽略</option>
                            <option value="escalate">升级处理</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>处理备注:</label>
                        <textarea id="handleNotes" class="form-control" rows="3" placeholder="请输入处理说明..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitAlarmHandle()">提交处理</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/chart.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/realtime_monitoring.js"></script>
</body>
</html>