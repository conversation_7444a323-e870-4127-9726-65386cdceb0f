<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常闭环管理 - 基于零售站点前置仓的巡检系统</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/exception_management.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    基于零售站点前置仓的巡检系统
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">异常闭环管理</h2>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索异常ID、描述或位置..." class="search-input">
                            <button class="search-btn" onclick="handleSearch()">🔍</button>
                        </div>
                        <div class="filter-box">
                            <select id="priorityFilter" onchange="handleFilter()">
                                <option value="">全部优先级</option>
                                <option value="紧急">紧急</option>
                                <option value="高">高</option>
                                <option value="中">中</option>
                                <option value="低">低</option>
                            </select>
                            <select id="statusFilter" onchange="handleFilter()">
                                <option value="">全部状态</option>
                                <option value="待处理">待处理</option>
                                <option value="处理中">处理中</option>
                                <option value="已解决">已解决</option>
                            </select>
                            <select id="slaFilter" onchange="handleFilter()">
                                <option value="">全部SLA</option>
                                <option value="正常">正常</option>
                                <option value="预警">预警</option>
                                <option value="超期">超期</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-secondary" onclick="refreshData()">刷新</button>
                        <button class="btn btn-primary" onclick="exportData()">导出CSV</button>
                        <button class="btn btn-success" onclick="showNewExceptionModal()">新建异常</button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">🔔</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalExceptions">0</div>
                            <div class="stat-label">总异常数量</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <div class="stat-number" id="criticalExceptions">0</div>
                            <div class="stat-label">严重异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="highExceptions">0</div>
                            <div class="stat-label">高级异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <div class="stat-number" id="pendingExceptions">0</div>
                            <div class="stat-label">待处理异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⌛</div>
                        <div class="stat-content">
                            <div class="stat-number" id="overdueExceptions">0</div>
                            <div class="stat-label">超期异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="slaWarningExceptions">0</div>
                            <div class="stat-label">SLA预警异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="resolvedExceptions">0</div>
                            <div class="stat-label">已解决异常</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="data-table" id="exceptionTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('exception_id')">异常ID <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('description')">异常描述 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('location')">位置 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('priority')">优先级 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('status')">状态 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('sla_status')">SLA状态 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('reporter')">报告人 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('created_at')">创建时间 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('updated_at')">更新时间 <span class="sort-indicator"></span></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将由JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div class="pagination-container" id="paginationContainer">
                    <!-- 分页控件将由JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 新建异常模态框 -->
    <div class="modal-overlay" id="newExceptionModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">新建异常</h3>
                <button class="modal-close" onclick="closeModal('newExceptionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="newExceptionForm">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="exceptionDescription">异常描述</label>
                                <input type="text" class="form-control" id="exceptionDescription" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="exceptionLocation">异常位置</label>
                                <input type="text" class="form-control" id="exceptionLocation" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="exceptionPriority">优先级</label>
                                <select class="form-control" id="exceptionPriority" required>
                                    <option value="紧急">紧急</option>
                                    <option value="高">高</option>
                                    <option value="中" selected>中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="exceptionReporter">报告人</label>
                                <input type="text" class="form-control" id="exceptionReporter" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="exceptionDetails">详细信息</label>
                        <textarea class="form-control" id="exceptionDetails" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上传图片证据（可选）</label>
                        <input type="file" class="form-control" id="exceptionImages" multiple accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('newExceptionModal')">取消</button>
                <button class="btn btn-primary" onclick="createException()">创建</button>
            </div>
        </div>
    </div>

    <!-- 异常详情模态框 -->
    <div class="modal-overlay" id="exceptionDetailModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">异常详情</h3>
                <button class="modal-close" onclick="closeModal('exceptionDetailModal')">&times;</button>
            </div>
            <div class="modal-body" id="exceptionDetailContent">
                <!-- 详情内容将由JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('exceptionDetailModal')">关闭</button>
                <button class="btn btn-primary" id="processExceptionBtn" onclick="showProcessExceptionModal()">处理异常</button>
            </div>
        </div>
    </div>

    <!-- 处理异常模态框 -->
    <div class="modal-overlay" id="processExceptionModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">处理异常</h3>
                <button class="modal-close" onclick="closeModal('processExceptionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="processExceptionForm">
                    <input type="hidden" id="processExceptionId">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="processAction">处理动作</label>
                                <select class="form-control" id="processAction" required onchange="handleActionChange()">
                                    <option value="处理中">开始处理</option>
                                    <option value="已解决">标记为已解决</option>
                                    <option value="升级">升级处理</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="processHandler">处理人</label>
                                <input type="text" class="form-control" id="processHandler" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="processComments">处理备注</label>
                        <textarea class="form-control" id="processComments" rows="4" required></textarea>
                    </div>
                    <div class="form-group" id="escalationGroupDiv" style="display: none;">
                        <label class="form-label" for="escalationGroup">升级处理组</label>
                        <select class="form-control" id="escalationGroup">
                            <option value="技术支持">技术支持</option>
                            <option value="运维团队">运维团队</option>
                            <option value="安全团队">安全团队</option>
                            <option value="管理层">管理层</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上传处理证据（可选）</label>
                        <input type="file" class="form-control" id="processImages" multiple accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('processExceptionModal')">取消</button>
                <button class="btn btn-primary" onclick="processException()">提交</button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/exception_management.js"></script>
</body>
</html>