/**
 * 多维度实时监控页面 JavaScript
 * 实现数据加载、图表渲染、实时更新等功能
 */

class RealtimeMonitoring {
    constructor() {
        this.data = null;
        this.charts = {};
        this.refreshInterval = null;
        this.currentFilters = {
            deviceType: 'all',
            personnel: 'all',
            alarmLevel: 'all'
        };
        this.init();
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            await this.loadData();
            this.setupEventListeners();
            this.initializeCharts();
            this.renderData();
            this.startAutoRefresh();
            console.log('多维度实时监控页面初始化完成');
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.showError('页面初始化失败，请刷新重试');
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            const response = await fetch('../data/realtime_monitoring.json');
            if (!response.ok) {
                throw new Error('数据加载失败');
            }
            const jsonData = await response.json();
            this.data = jsonData.realtime_monitoring || {};
            this.updateLastUpdateTime();
        } catch (error) {
            console.error('数据加载失败:', error);
            throw error;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 设备类型筛选
        $('#deviceTypeFilter').on('change', (e) => {
            this.currentFilters.deviceType = e.target.value;
            this.renderDeviceList();
        });

        // 人员筛选
        $('#personnelFilter').on('change', (e) => {
            this.currentFilters.personnel = e.target.value;
            this.renderPersonnelData();
        });

        // 报警级别筛选
        $('#alarmLevelFilter').on('change', (e) => {
            this.currentFilters.alarmLevel = e.target.value;
            this.renderAlarmList();
        });

        // 图表控制
        $('#chartDataType, #chartTimeRange').on('change', () => {
            this.updateRealtimeChart();
        });

        // 模态框事件
        $(document).on('click', '.device-item', (e) => {
            const deviceId = $(e.currentTarget).data('device-id');
            this.showDeviceDetail(deviceId);
        });

        $(document).on('click', '.alarm-item', (e) => {
            const alarmId = $(e.currentTarget).data('alarm-id');
            this.showAlarmHandle(alarmId);
        });
    }

    /**
     * 初始化图表
     */
    initializeCharts() {
        const ctx = document.getElementById('realtimeChart');
        if (ctx) {
            this.charts.realtime = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }
    }

    /**
     * 渲染所有数据
     */
    renderData() {
        this.renderStatistics();
        this.renderDeviceList();
        this.renderEnvironmentData();
        this.renderPersonnelData();
        this.renderAlarmList();
        this.updateRealtimeChart();
    }

    /**
     * 渲染统计概览
     */
    renderStatistics() {
        if (!this.data.statistics) return;

        const stats = this.data.statistics;
        $('#totalDevices').text(stats.totalDevices);
        $('#normalDevices').text(stats.normalDevices);
        $('#warningDevices').text(stats.warningDevices);
        $('#errorDevices').text(stats.errorDevices);
        $('#activePersonnel').text(stats.activePersonnel);
        $('#totalPersonnel').text(stats.totalPersonnel);
        $('#totalAlarms').text(stats.totalAlarms);
        $('#criticalAlarms').text(stats.criticalAlarms);
        $('#warningAlarms').text(stats.warningAlarms);
        $('#systemHealth').text(stats.systemHealth + '%');
        $('#operationEfficiency').text(stats.operationEfficiency + '%');
    }

    /**
     * 渲染设备列表
     */
    renderDeviceList() {
        if (!this.data.devices) return;

        let devices = this.data.devices;

        // 应用筛选
        if (this.currentFilters.deviceType !== 'all') {
            devices = devices.filter(device => device.type === this.currentFilters.deviceType);
        }

        const deviceListHtml = devices.map(device => `
            <div class="device-item" data-device-id="${device.id}">
                <div class="device-header">
                    <span class="device-name">${device.name}</span>
                    <span class="device-status ${device.status}">${this.getStatusText(device.status)}</span>
                </div>
                <div class="device-info">
                    <div class="info-item">
                        <span class="info-label">当前值:</span>
                        <span class="info-value">${device.value} ${device.unit}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">位置:</span>
                        <span class="info-value">${device.location}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">阈值:</span>
                        <span class="info-value">${device.threshold.min}~${device.threshold.max} ${device.unit}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">更新时间:</span>
                        <span class="info-value">${device.lastUpdate}</span>
                    </div>
                </div>
            </div>
        `).join('');

        $('#deviceList').html(deviceListHtml);
    }

    /**
     * 渲染环境数据
     */
    renderEnvironmentData() {
        if (!this.data.environment) return;

        const environmentHtml = this.data.environment.map(env => `
            <div class="environment-card">
                <div class="environment-header">
                    <span class="environment-area">${env.area}</span>
                    <div class="environment-status ${env.status}"></div>
                </div>
                <div class="environment-metrics">
                    <div class="metric-item">
                        <span class="metric-label">温度:</span>
                        <span class="metric-value">${env.temperature}°C</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">湿度:</span>
                        <span class="metric-value">${env.humidity}%</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">空气质量:</span>
                        <span class="metric-value">${env.airQuality}</span>
                    </div>
                    <div class="metric-item">
                        <span class="metric-label">噪音:</span>
                        <span class="metric-value">${env.noiseLevel}dB</span>
                    </div>
                </div>
            </div>
        `).join('');

        $('#environmentGrid').html(environmentHtml);
    }

    /**
     * 渲染人员数据
     */
    renderPersonnelData() {
        if (!this.data.personnel) return;

        let personnel = this.data.personnel;

        // 应用筛选
        if (this.currentFilters.personnel === 'active') {
            personnel = personnel.filter(p => p.status === 'active');
        } else if (this.currentFilters.personnel === 'offline') {
            personnel = personnel.filter(p => p.status === 'offline');
        }

        // 渲染人员地图
        this.renderPersonnelMap();

        // 渲染人员列表
        const personnelHtml = personnel.map(person => `
            <div class="personnel-item">
                <div class="personnel-header">
                    <span class="personnel-name">${person.name}</span>
                    <div class="personnel-status ${person.status}"></div>
                </div>
                <div class="personnel-info">
                    <div>职位: ${person.position}</div>
                    <div>当前区域: ${person.currentArea}</div>
                    <div>设备: ${person.deviceId}</div>
                    <div>最后活动: ${person.lastActivity}</div>
                </div>
            </div>
        `).join('');

        $('#personnelList').html(personnelHtml);
    }

    /**
     * 渲染人员地图
     */
    renderPersonnelMap() {
        const areas = ['A区', 'B区', 'C区', 'D区', 'E区', 'F区', '装卸区', '办公区', '机房A', '机房B'];
        const personnelByArea = {};

        this.data.personnel.forEach(person => {
            if (!personnelByArea[person.currentArea]) {
                personnelByArea[person.currentArea] = [];
            }
            personnelByArea[person.currentArea].push(person);
        });

        const mapHtml = areas.map((area, index) => {
            const hasPersonnel = personnelByArea[area] && personnelByArea[area].length > 0;
            const count = hasPersonnel ? personnelByArea[area].length : 0;
            const left = (index % 4) * 25 + 10;
            const top = Math.floor(index / 4) * 40 + 20;

            return `
                <div class="area-marker ${hasPersonnel ? 'has-personnel' : ''}"
                     style="left: ${left}%; top: ${top}%;"
                     title="${area}: ${count}人">
                    ${area.charAt(0)}${count}
                </div>
            `;
        }).join('');

        $('#personnelMap').html(mapHtml);
    }

    /**
     * 渲染报警列表
     */
    renderAlarmList() {
        if (!this.data.alarms) return;

        let alarms = this.data.alarms;

        // 应用筛选
        if (this.currentFilters.alarmLevel !== 'all') {
            alarms = alarms.filter(alarm => alarm.level === this.currentFilters.alarmLevel);
        }

        const alarmHtml = alarms.map(alarm => `
            <div class="alarm-item ${alarm.level}" data-alarm-id="${alarm.id}">
                <div class="alarm-header">
                    <span class="alarm-level ${alarm.level}">${this.getAlarmLevelText(alarm.level)}</span>
                    <span class="alarm-time">${alarm.timestamp}</span>
                </div>
                <div class="alarm-message">${alarm.message}</div>
                <div class="alarm-location">位置: ${alarm.area}</div>
            </div>
        `).join('');

        $('#alarmList').html(alarmHtml);
    }

    /**
     * 更新实时图表
     */
    updateRealtimeChart() {
        if (!this.charts.realtime) return;

        const dataType = $('#chartDataType').val();
        const timeRange = $('#chartTimeRange').val();

        let chartData = {
            labels: [],
            datasets: []
        };

        if (dataType === 'temperature') {
            chartData = this.generateTemperatureChartData(timeRange);
        } else if (dataType === 'humidity') {
            chartData = this.generateHumidityChartData(timeRange);
        } else if (dataType === 'energy') {
            chartData = this.generateEnergyChartData(timeRange);
        } else if (dataType === 'efficiency') {
            chartData = this.generateEfficiencyChartData(timeRange);
        }

        this.charts.realtime.data = chartData;
        this.charts.realtime.update();
    }

    /**
     * 生成温度图表数据
     */
    generateTemperatureChartData(timeRange) {
        const timeLabels = this.generateTimeLabels(timeRange);
        const temperatureDevices = this.data.devices.filter(d => d.type === 'temperature');

        return {
            labels: timeLabels,
            datasets: temperatureDevices.map((device, index) => ({
                label: device.name,
                data: this.generateRandomData(timeLabels.length, device.value - 5, device.value + 5),
                borderColor: this.getChartColor(index),
                backgroundColor: this.getChartColor(index, 0.1),
                tension: 0.4
            }))
        };
    }

    /**
     * 生成湿度图表数据
     */
    generateHumidityChartData(timeRange) {
        const timeLabels = this.generateTimeLabels(timeRange);
        const humidityDevices = this.data.devices.filter(d => d.type === 'humidity');

        return {
            labels: timeLabels,
            datasets: humidityDevices.map((device, index) => ({
                label: device.name,
                data: this.generateRandomData(timeLabels.length, device.value - 10, device.value + 10),
                borderColor: this.getChartColor(index),
                backgroundColor: this.getChartColor(index, 0.1),
                tension: 0.4
            }))
        };
    }

    /**
     * 生成能耗图表数据
     */
    generateEnergyChartData(timeRange) {
        const timeLabels = this.generateTimeLabels(timeRange);
        const baseEnergy = this.data.statistics.energyConsumption;

        return {
            labels: timeLabels,
            datasets: [{
                label: '能耗 (kWh)',
                data: this.generateRandomData(timeLabels.length, baseEnergy * 0.8, baseEnergy * 1.2),
                borderColor: '#ff6b6b',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                tension: 0.4
            }]
        };
    }

    /**
     * 生成效率图表数据
     */
    generateEfficiencyChartData(timeRange) {
        const timeLabels = this.generateTimeLabels(timeRange);
        const baseEfficiency = this.data.statistics.operationEfficiency;

        return {
            labels: timeLabels,
            datasets: [{
                label: '运营效率 (%)',
                data: this.generateRandomData(timeLabels.length, baseEfficiency - 5, baseEfficiency + 5),
                borderColor: '#4ecdc4',
                backgroundColor: 'rgba(78, 205, 196, 0.1)',
                tension: 0.4
            }]
        };
    }

    /**
     * 生成时间标签
     */
    generateTimeLabels(timeRange) {
        const now = new Date();
        const labels = [];
        let interval, count;

        switch (timeRange) {
            case '1h':
                interval = 5; // 5分钟间隔
                count = 12;
                break;
            case '6h':
                interval = 30; // 30分钟间隔
                count = 12;
                break;
            case '24h':
                interval = 120; // 2小时间隔
                count = 12;
                break;
            default:
                interval = 5;
                count = 12;
        }

        for (let i = count - 1; i >= 0; i--) {
            const time = new Date(now - i * interval * 60000);
            labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
        }

        return labels;
    }

    /**
     * 生成随机数据
     */
    generateRandomData(length, min, max) {
        return Array.from({ length }, () =>
            Math.round((Math.random() * (max - min) + min) * 100) / 100
        );
    }

    /**
     * 获取图表颜色
     */
    getChartColor(index, alpha = 1) {
        const colors = [
            '#0052d9', '#00a870', '#ed7b2f', '#d54941',
            '#9c27b0', '#607d8b', '#ff5722', '#795548'
        ];
        const color = colors[index % colors.length];

        if (alpha < 1) {
            // 转换为 rgba
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }

        return color;
    }

    /**
     * 显示设备详情
     */
    showDeviceDetail(deviceId) {
        const device = this.data.devices.find(d => d.id === deviceId);
        if (!device) return;

        const content = `
            <div class="device-detail">
                <h5>${device.name}</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>设备ID:</label>
                        <span>${device.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>设备类型:</label>
                        <span>${this.getDeviceTypeText(device.type)}</span>
                    </div>
                    <div class="detail-item">
                        <label>当前状态:</label>
                        <span class="status ${device.status}">${this.getStatusText(device.status)}</span>
                    </div>
                    <div class="detail-item">
                        <label>当前值:</label>
                        <span>${device.value} ${device.unit}</span>
                    </div>
                    <div class="detail-item">
                        <label>正常范围:</label>
                        <span>${device.threshold.min} ~ ${device.threshold.max} ${device.unit}</span>
                    </div>
                    <div class="detail-item">
                        <label>安装位置:</label>
                        <span>${device.location}</span>
                    </div>
                    <div class="detail-item">
                        <label>最后更新:</label>
                        <span>${device.lastUpdate}</span>
                    </div>
                </div>
            </div>
        `;

        $('#deviceDetailContent').html(content);
        $('#deviceDetailModal').modal('show');
    }

    /**
     * 显示报警处理
     */
    showAlarmHandle(alarmId) {
        const alarm = this.data.alarms.find(a => a.id === alarmId);
        if (!alarm) return;

        // 保存当前处理的报警ID
        this.currentAlarmId = alarmId;
        $('#alarmHandleModal').modal('show');
    }

    /**
     * 提交报警处理
     */
    submitAlarmHandle() {
        const action = $('#handleAction').val();
        const notes = $('#handleNotes').val();

        if (!notes.trim()) {
            alert('请输入处理说明');
            return;
        }

        // 模拟处理报警
        console.log('处理报警:', {
            alarmId: this.currentAlarmId,
            action: action,
            notes: notes
        });

        // 更新报警状态
        const alarm = this.data.alarms.find(a => a.id === this.currentAlarmId);
        if (alarm) {
            alarm.status = 'handled';
        }

        $('#alarmHandleModal').modal('hide');
        $('#handleNotes').val('');
        this.renderAlarmList();

        this.showSuccess('报警处理成功');
    }

    /**
     * 导出设备报告
     */
    exportDeviceReport() {
        // 模拟导出功能
        const reportData = {
            timestamp: new Date().toISOString(),
            devices: this.data.devices,
            statistics: this.data.statistics
        };

        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `设备监控报告_${new Date().toLocaleDateString('zh-CN')}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showSuccess('报告导出成功');
    }

    /**
     * 刷新数据
     */
    async refreshDeviceData() {
        await this.loadData();
        this.renderDeviceList();
        this.showSuccess('设备数据已刷新');
    }

    async refreshEnvironmentData() {
        await this.loadData();
        this.renderEnvironmentData();
        this.showSuccess('环境数据已刷新');
    }

    async refreshPersonnelData() {
        await this.loadData();
        this.renderPersonnelData();
        this.showSuccess('人员数据已刷新');
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(async () => {
            try {
                await this.loadData();
                this.renderStatistics();
                this.updateLastUpdateTime();
            } catch (error) {
                console.error('自动刷新失败:', error);
            }
        }, 30000); // 30秒刷新一次
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const now = new Date().toLocaleString('zh-CN');
        $('#lastUpdateTime').text(now);
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'normal': '正常',
            'warning': '警告',
            'error': '故障',
            'offline': '离线',
            'active': '在线'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取设备类型文本
     */
    getDeviceTypeText(type) {
        const typeMap = {
            'temperature': '温度传感器',
            'humidity': '湿度传感器',
            'equipment': '设备',
            'ventilation': '通风系统',
            'lighting': '照明系统',
            'access': '门禁系统',
            'camera': '监控摄像头'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取报警级别文本
     */
    getAlarmLevelText(level) {
        const levelMap = {
            'critical': '严重',
            'warning': '警告',
            'info': '信息'
        };
        return levelMap[level] || level;
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 可以使用 toast 或其他通知组件
        console.log('成功:', message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 可以使用 toast 或其他通知组件
        console.error('错误:', message);
    }
}

// 全局函数供HTML调用
window.refreshDeviceData = () => realtimeMonitoring.refreshDeviceData();
window.refreshEnvironmentData = () => realtimeMonitoring.refreshEnvironmentData();
window.refreshPersonnelData = () => realtimeMonitoring.refreshPersonnelData();
window.exportDeviceReport = () => realtimeMonitoring.exportDeviceReport();
window.submitAlarmHandle = () => realtimeMonitoring.submitAlarmHandle();

// 页面加载完成后初始化
let realtimeMonitoring;
$(document).ready(() => {
    realtimeMonitoring = new RealtimeMonitoring();
});

// 页面卸载时清理
$(window).on('beforeunload', () => {
    if (realtimeMonitoring) {
        realtimeMonitoring.stopAutoRefresh();
    }
});