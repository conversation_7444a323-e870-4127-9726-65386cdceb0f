// 异常闭环管理页面JavaScript

// 数据管理器
class ExceptionDataManager {
    constructor() {
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = '';
        this.sortOrder = 'asc';
    }

    async loadData() {
        try {
            const response = await fetch('/data/exception_management.json');
            if (!response.ok) {
                throw new Error('Failed to load data');
            }
            this.data = await response.json();
            this.filteredData = [...this.data];
            this.updateStats();
            this.renderTable();
            this.renderPagination();
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败，请刷新页面重试');
        }
    }

    updateStats() {
        const total = this.data.length;
        const critical = this.data.filter(item => item.priority === '紧急').length;
        const high = this.data.filter(item => item.priority === '高').length;
        const pending = this.data.filter(item => item.status === '待处理').length;
        const overdue = this.data.filter(item => item.sla_status === '超期').length;

        const setStat = (id, value) => {
            const el = document.getElementById(id);
            if (el) el.textContent = value;
        };

        setStat('totalExceptions', total);
        setStat('criticalExceptions', critical);
        setStat('highExceptions', high);
        setStat('pendingExceptions', pending);
        setStat('overdueExceptions', overdue);
    }

    filter(filters) {
        this.filteredData = this.data.filter(item => {
            return Object.keys(filters).every(key => {
                if (!filters[key]) return true;
                return item[key] === filters[key];
            });
        });
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    search(query) {
        if (!query) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(item =>
                item.exception_id.toLowerCase().includes(query.toLowerCase()) ||
                item.description.toLowerCase().includes(query.toLowerCase()) ||
                item.location.toLowerCase().includes(query.toLowerCase()) ||
                item.reporter.toLowerCase().includes(query.toLowerCase())
            );
        }
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    sort(field) {
        if (this.sortField === field) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (aVal < bVal) return this.sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTable();
        this.updateSortIndicators();
    }

    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.textContent = '';
        });

        const currentHeader = document.querySelector(`th[onclick="sortTable('${this.sortField}')"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.textContent = this.sortOrder === 'asc' ? '↑' : '↓';
        }
    }

    renderTable() {
        const tbody = document.getElementById('tableBody');
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        const pageData = this.filteredData.slice(start, end);

        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="empty-state">
                        <div class="empty-state-icon">📋</div>
                        <div class="empty-state-text">暂无数据</div>
                        <div class="empty-state-desc">没有找到符合条件的异常记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = pageData.map(item => `
            <tr>
                <td>${item.exception_id}</td>
                <td>${item.description}</td>
                <td>${item.location}</td>
                <td><span class="priority-badge ${item.priority === '紧急' ? 'critical' : item.priority === '高' ? 'high' : item.priority === '中' ? 'medium' : 'low'}">${item.priority}</span></td>
                <td><span class="status-badge ${item.status === '待处理' ? 'pending' : item.status === '处理中' ? 'processing' : item.status === '已解决' ? 'resolved' : 'closed'}">${item.status}</span></td>
                <td>
                    <div class="sla-status ${item.sla_status === '正常' ? 'normal' : item.sla_status === '预警' ? 'warning' : 'overdue'}">
                        <span class="sla-indicator"></span>
                        ${item.sla_status}
                    </div>
                </td>
                <td>${item.reporter}</td>
                <td>${item.created_at}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-action view" onclick="viewException('${item.exception_id}')">查看</button>
                        ${item.status === '待处理' ? `<button class="btn-action process" onclick="processException('${item.exception_id}')">处理</button>` : ''}
                        ${item.status === '处理中' ? `<button class="btn-action resolve" onclick="resolveException('${item.exception_id}')">解决</button>` : ''}
                        ${item.status === '已解决' ? `<button class="btn-action close" onclick="closeException('${item.exception_id}')">关闭</button>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderPagination() {
        const container = document.getElementById('paginationContainer');
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination">';

        // 上一页
        html += `<button class="page-btn" ${this.currentPage === 1 ? 'disabled' : ''} onclick="changePage(${this.currentPage - 1})">上一页</button>`;

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                html += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
                html += `<button class="page-btn" onclick="changePage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }

        // 下一页
        html += `<button class="page-btn" ${this.currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${this.currentPage + 1})">下一页</button>`;

        html += '</div>';
        container.innerHTML = html;
    }

    changePage(page) {
        this.currentPage = page;
        this.renderTable();
        this.renderPagination();
    }

    showError(message) {
        showToast(message, 'error');
    }
}

// 全局变量
let dataManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dataManager = new ExceptionDataManager();
    dataManager.loadData();

    // 绑定事件监听器
    bindEventListeners();
});

// 绑定事件监听器
function bindEventListeners() {
    // 搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            dataManager.search(this.value);
        }, 300));
    }

    // 筛选器
    const filters = ['statusFilter', 'priorityFilter', 'slaFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleFilter);
        }
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理筛选
function handleFilter() {
    const filters = {
        status: document.getElementById('statusFilter')?.value || '',
        priority: document.getElementById('priorityFilter')?.value || '',
        sla_status: document.getElementById('slaFilter')?.value || ''
    };
    dataManager.filter(filters);
}

// 排序表格
function sortTable(field) {
    dataManager.sort(field);
}

// 切换页面
function changePage(page) {
    dataManager.changePage(page);
}

// 刷新数据
function refreshData() {
    dataManager.loadData();
    showToast('数据已刷新', 'success');
}

// 导出数据
function exportData() {
    const csvContent = convertToCSV(dataManager.filteredData);
    downloadCSV(csvContent, 'exception_management.csv');
    showToast('数据导出成功', 'success');
}

// 转换为CSV格式
function convertToCSV(data) {
    const headers = ['异常编号', '异常描述', '位置', '优先级', '状态', 'SLA状态', '报告人', '创建时间'];
    const rows = data.map(item => [
        item.exception_id,
        item.description,
        item.location,
        item.priority,
        item.status,
        item.sla_status,
        item.reporter,
        item.created_at
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// 下载CSV文件
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示新建异常模态框
function showNewExceptionModal() {
    const modal = document.getElementById('newExceptionModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 查看异常详情
function viewException(exceptionId) {
    const exception = dataManager.data.find(item => item.exception_id === exceptionId);
    if (!exception) return;

    const modal = document.getElementById('viewExceptionModal');
    if (modal) {
        // 填充详情数据
        document.getElementById('viewExceptionId').textContent = exception.exception_id;
        document.getElementById('viewDescription').textContent = exception.description;
        document.getElementById('viewLocation').textContent = exception.location;
        document.getElementById('viewPriority').innerHTML = `<span class="priority-badge ${exception.priority === '紧急' ? 'critical' : exception.priority === '高' ? 'high' : exception.priority === '中' ? 'medium' : 'low'}">${exception.priority}</span>`;
        document.getElementById('viewStatus').innerHTML = `<span class="status-badge ${exception.status === '待处理' ? 'pending' : exception.status === '处理中' ? 'processing' : exception.status === '已解决' ? 'resolved' : 'closed'}">${exception.status}</span>`;
        document.getElementById('viewSlaStatus').innerHTML = `<div class="sla-status ${exception.sla_status === '正常' ? 'normal' : exception.sla_status === '预警' ? 'warning' : 'overdue'}"><span class="sla-indicator"></span>${exception.sla_status}</div>`;
        document.getElementById('viewReporter').textContent = exception.reporter;
        document.getElementById('viewCreatedAt').textContent = exception.created_at;
        document.getElementById('viewUpdatedAt').textContent = exception.updated_at || '暂无';

        modal.style.display = 'block';
    }
}

// 处理异常
function processException(exceptionId) {
    const exception = dataManager.data.find(item => item.exception_id === exceptionId);
    if (!exception) return;

    const modal = document.getElementById('processExceptionModal');
    if (modal) {
        document.getElementById('processExceptionId').value = exceptionId;
        modal.style.display = 'block';
    }
}

// 解决异常
function resolveException(exceptionId) {
    const exception = dataManager.data.find(item => item.exception_id === exceptionId);
    if (!exception) return;

    const modal = document.getElementById('resolveExceptionModal');
    if (modal) {
        document.getElementById('resolveExceptionId').value = exceptionId;
        modal.style.display = 'block';
    }
}

// 关闭异常
function closeException(exceptionId) {
    if (confirm('确定要关闭此异常吗？')) {
        // 模拟关闭操作
        const exception = dataManager.data.find(item => item.exception_id === exceptionId);
        if (exception) {
            exception.status = '已关闭';
            exception.updated_at = new Date().toLocaleString();
            dataManager.updateStats();
            dataManager.renderTable();
            showToast('异常已关闭', 'success');
        }
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// 提交新建异常
function submitNewException() {
    const formData = new FormData(document.getElementById('newExceptionForm'));
    const data = Object.fromEntries(formData);

    // 模拟提交操作
    console.log('提交新异常:', data);
    closeModal('newExceptionModal');
    showToast('异常已创建', 'success');
}

// 提交处理异常
function submitProcessException() {
    const formData = new FormData(document.getElementById('processExceptionForm'));
    const data = Object.fromEntries(formData);

    // 模拟提交操作
    const exception = dataManager.data.find(item => item.exception_id === data.exception_id);
    if (exception) {
        exception.status = '处理中';
        exception.updated_at = new Date().toLocaleString();
        dataManager.updateStats();
        dataManager.renderTable();
    }

    closeModal('processExceptionModal');
    showToast('异常处理已开始', 'success');
}

// 提交解决异常
function submitResolveException() {
    const formData = new FormData(document.getElementById('resolveExceptionForm'));
    const data = Object.fromEntries(formData);

    // 模拟提交操作
    const exception = dataManager.data.find(item => item.exception_id === data.exception_id);
    if (exception) {
        exception.status = '已解决';
        exception.updated_at = new Date().toLocaleString();
        dataManager.updateStats();
        dataManager.renderTable();
    }

    closeModal('resolveExceptionModal');
    showToast('异常已解决', 'success');
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;

    switch (type) {
        case 'success':
            toast.style.backgroundColor = '#52c41a';
            break;
        case 'error':
            toast.style.backgroundColor = '#ff4d4f';
            break;
        case 'warning':
            toast.style.backgroundColor = '#faad14';
            break;
        default:
            toast.style.backgroundColor = '#1890ff';
    }

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
};