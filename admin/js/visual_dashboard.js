/**
 * 可视化看板与预警页面 JavaScript
 * 实现仪表板渲染、图表展示、预警管理等功能
 */

class VisualDashboard {
    constructor() {
        this.data = null;
        this.charts = {};
        this.refreshInterval = null;
        this.currentTimeRange = 'realtime';
        this.init();
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            await this.loadData();
            this.setupEventListeners();
            this.initializeCharts();
            this.renderDashboard();
            this.startAutoRefresh();
            console.log('可视化看板页面初始化完成');
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.showError('页面初始化失败，请刷新重试');
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            const response = await fetch('../data/visual_dashboard.json');
            if (!response.ok) {
                throw new Error('数据加载失败');
            }
            const jsonData = await response.json();
            this.data = jsonData.visual_dashboard || {};
        } catch (error) {
            console.error('数据加载失败:', error);
            throw error;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 时间范围筛选
        $('#timeRangeFilter').on('change', (e) => {
            this.currentTimeRange = e.target.value;
            this.refreshDashboard();
        });

        // 预测时间范围筛选
        $('#predictionTimeframe').on('change', () => {
            this.renderFaultPrediction();
        });

        // 温度视图切换
        $('#temperatureView').on('change', () => {
            this.renderTemperatureHeatmap();
        });

        // 模态框事件
        $(document).on('click', '.alert-item', (e) => {
            const alertId = $(e.currentTarget).data('alert-id');
            this.showAlertDetail(alertId);
        });

        $(document).on('click', '.prediction-item', (e) => {
            const deviceId = $(e.currentTarget).data('device-id');
            this.showPredictionDetail(deviceId);
        });

        $(document).on('click', '.heatmap-cell', (e) => {
            const area = $(e.currentTarget).data('area');
            this.showAreaDetail(area);
        });
    }

    /**
     * 初始化图表
     */
    initializeCharts() {
        this.initHealthScoreGauge();
        this.initHealthTrendChart();
        this.initEquipmentStatusChart();
        this.initEnergyTrendChart();
        this.initThroughputChart();
    }

    /**
     * 初始化健康度仪表盘
     */
    initHealthScoreGauge() {
        const canvas = document.getElementById('healthScoreGauge');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.healthGauge = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0, 100],
                    backgroundColor: ['#0052d9', '#f0f0f0'],
                    borderWidth: 0,
                    cutout: '80%'
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                }
            }
        });
    }

    /**
     * 初始化健康度趋势图
     */
    initHealthTrendChart() {
        const canvas = document.getElementById('healthTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.healthTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '健康度',
                    data: [],
                    borderColor: '#0052d9',
                    backgroundColor: 'rgba(0, 82, 217, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: { display: false }
                }
            }
        });
    }

    /**
     * 初始化设备状态图表
     */
    initEquipmentStatusChart() {
        const canvas = document.getElementById('equipmentStatusChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.equipmentStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['正常', '警告', '故障', '维护'],
                datasets: [{
                    data: [],
                    backgroundColor: ['#00a870', '#ed7b2f', '#d54941', '#666666'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    /**
     * 初始化能耗趋势图
     */
    initEnergyTrendChart() {
        const canvas = document.getElementById('energyTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.energyTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '能耗',
                    data: [],
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: { beginAtZero: true }
                },
                plugins: {
                    legend: { display: false }
                }
            }
        });
    }

    /**
     * 初始化吞吐量图表
     */
    initThroughputChart() {
        const canvas = document.getElementById('throughputChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        this.charts.throughput = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: '吞吐量',
                    data: [],
                    backgroundColor: '#4ecdc4',
                    borderColor: '#4ecdc4',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: { beginAtZero: true }
                },
                plugins: {
                    legend: { display: false }
                }
            }
        });
    }

    /**
     * 渲染整个仪表板
     */
    renderDashboard() {
        this.renderHealthCockpit();
        this.renderKPIMetrics();
        this.renderEquipmentStatus();
        this.renderFaultPrediction();
        this.renderOperationData();
        this.renderTemperatureHeatmap();
        this.renderAlerts();
        this.renderRealTimeStats();
    }

    /**
     * 渲染运营健康度驾驶舱
     */
    renderHealthCockpit() {
        if (!this.data.healthScore) return;

        const healthScore = this.data.healthScore;

        // 更新总体健康度
        $('#overallHealthScore').text(healthScore.overall);

        // 更新健康度仪表盘
        if (this.charts.healthGauge) {
            this.charts.healthGauge.data.datasets[0].data = [healthScore.overall, 100 - healthScore.overall];
            this.charts.healthGauge.update();
        }

        // 更新各类别进度条和分数
        Object.keys(healthScore.categories).forEach(category => {
            const score = healthScore.categories[category];
            $(`#${category}Progress`).css('width', `${score}%`);
            $(`#${category}Score`).text(`${score}%`);
        });

        // 更新健康度趋势图
        if (this.charts.healthTrend && healthScore.trend) {
            this.charts.healthTrend.data.labels = healthScore.trend.map(item => item.time);
            this.charts.healthTrend.data.datasets[0].data = healthScore.trend.map(item => item.score);
            this.charts.healthTrend.update();
        }
    }

    /**
     * 渲染KPI指标
     */
    renderKPIMetrics() {
        if (!this.data.kpiMetrics) return;

        const kpiHtml = this.data.kpiMetrics.map(kpi => `
            <div class="kpi-card">
                <div class="kpi-name">${kpi.name}</div>
                <div class="kpi-value">${kpi.value}<span class="kpi-unit">${kpi.unit}</span></div>
                <div class="kpi-trend ${kpi.trend}">
                    ${this.getTrendIcon(kpi.trend)} ${Math.abs(kpi.change)}${kpi.unit}
                </div>
                <div class="kpi-target">目标: ${kpi.target}${kpi.unit}</div>
            </div>
        `).join('');

        $('#kpiGrid').html(kpiHtml);
    }

    /**
     * 渲染设备状态
     */
    renderEquipmentStatus() {
        if (!this.data.equipmentStatus) return;

        const equipment = this.data.equipmentStatus;

        // 更新设备状态图表
        if (this.charts.equipmentStatus) {
            this.charts.equipmentStatus.data.datasets[0].data = [
                equipment.normal, equipment.warning, equipment.error, equipment.maintenance
            ];
            this.charts.equipmentStatus.update();
        }

        // 更新设备统计信息
        const statsHtml = `
            <div class="stat-row">
                <span class="stat-label">设备总数</span>
                <span class="stat-value">${equipment.total}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">正常运行</span>
                <span class="stat-value" style="color: #00a870">${equipment.normal}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">警告状态</span>
                <span class="stat-value" style="color: #ed7b2f">${equipment.warning}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">故障设备</span>
                <span class="stat-value" style="color: #d54941">${equipment.error}</span>
            </div>
            <div class="stat-row">
                <span class="stat-label">维护中</span>
                <span class="stat-value" style="color: #666666">${equipment.maintenance}</span>
            </div>
        `;
        $('#equipmentStats').html(statsHtml);

        // 更新设备分类统计
        const categoriesHtml = equipment.categories.map(category => `
            <div class="category-card">
                <div class="category-name">${category.name}</div>
                <div class="category-stats">
                    <span class="normal">${category.normal}</span>
                    <span class="warning">${category.warning}</span>
                    <span class="error">${category.error}</span>
                </div>
            </div>
        `).join('');
        $('#equipmentCategories').html(categoriesHtml);
    }

    /**
     * 渲染故障预测
     */
    renderFaultPrediction() {
        if (!this.data.faultPrediction) return;

        const timeframe = $('#predictionTimeframe').val() || '7d';
        // 根据时间范围筛选预测数据
        let predictions = this.data.faultPrediction;

        const predictionHtml = predictions.map(prediction => `
            <div class="prediction-item ${this.getSeverityClass(prediction.severity)}" data-device-id="${prediction.deviceId}">
                <div class="prediction-header">
                    <span class="prediction-device">${prediction.deviceName}</span>
                    <span class="prediction-probability ${this.getSeverityClass(prediction.severity)}">${prediction.probability}%</span>
                </div>
                <div class="prediction-details">
                    <div class="prediction-fault">故障类型: ${prediction.faultType}</div>
                    <div class="prediction-time">预计时间: ${prediction.predictedTime}</div>
                </div>
                <div class="prediction-advice">${prediction.maintenanceAdvice}</div>
            </div>
        `).join('');

        $('#predictionList').html(predictionHtml);
    }

    /**
     * 渲染运营数据
     */
    renderOperationData() {
        if (!this.data.operationData) return;

        const operation = this.data.operationData;

        // 更新能耗数据
        $('#currentEnergy').text(operation.energyConsumption.today);
        const energyChange = ((operation.energyConsumption.today - operation.energyConsumption.yesterday) / operation.energyConsumption.yesterday * 100).toFixed(1);
        $('#energyComparison').text(`${energyChange > 0 ? '+' : ''}${energyChange}%`)
            .removeClass('positive negative')
            .addClass(energyChange > 0 ? 'positive' : 'negative');

        // 更新能耗趋势图
        if (this.charts.energyTrend && operation.energyConsumption.trend) {
            this.charts.energyTrend.data.labels = operation.energyConsumption.trend.map(item => `${item.hour}:00`);
            this.charts.energyTrend.data.datasets[0].data = operation.energyConsumption.trend.map(item => item.value);
            this.charts.energyTrend.update();
        }

        // 更新运营效率
        $('#currentEfficiency').text(operation.efficiency.current);
        $('#efficiencyTarget').text(operation.efficiency.target + '%');

        const efficiencyAreasHtml = operation.efficiency.byArea.map(area => `
            <div class="area-efficiency">
                <span class="area-name">${area.area}</span>
                <span class="area-value">${area.efficiency}%</span>
            </div>
        `).join('');
        $('#efficiencyAreas').html(efficiencyAreasHtml);

        // 更新吞吐量
        $('#currentThroughput').text(operation.throughput.today);
        const throughputPercentage = (operation.throughput.today / operation.throughput.target * 100).toFixed(1);
        $('#throughputPercentage').text(throughputPercentage + '%');
        $('#throughputProgress').css('width', `${throughputPercentage}%`);

        // 更新吞吐量图表
        if (this.charts.throughput && operation.throughput.hourly) {
            this.charts.throughput.data.labels = operation.throughput.hourly.map(item => `${item.hour}:00`);
            this.charts.throughput.data.datasets[0].data = operation.throughput.hourly.map(item => item.value);
            this.charts.throughput.update();
        }
    }

    /**
     * 渲染温度热力图
     */
    renderTemperatureHeatmap() {
        if (!this.data.temperatureMap) return;

        const heatmapHtml = this.data.temperatureMap.map(cell => `
            <div class="heatmap-cell ${cell.status}" data-area="${cell.area}">
                <div class="cell-area">${cell.area}</div>
                <div class="cell-temp">${cell.temperature}°C</div>
            </div>
        `).join('');

        $('#temperatureHeatmap').html(heatmapHtml);
    }

    /**
     * 渲染预警列表
     */
    renderAlerts() {
        if (!this.data.alerts) return;

        // 更新预警计数
        const criticalCount = this.data.alerts.filter(alert => alert.level === 'critical').length;
        const warningCount = this.data.alerts.filter(alert => alert.level === 'warning').length;
        const infoCount = this.data.alerts.filter(alert => alert.level === 'info').length;

        $('#criticalAlertCount').text(criticalCount);
        $('#warningAlertCount').text(warningCount);
        $('#infoAlertCount').text(infoCount);

        // 渲染预警列表
        const alertHtml = this.data.alerts.map(alert => `
            <div class="alert-item ${alert.level}" data-alert-id="${alert.id}">
                <div class="alert-header">
                    <span class="alert-title">${alert.title}</span>
                    <span class="alert-time">${alert.time}</span>
                </div>
                <div class="alert-message">${alert.message}</div>
                <div class="alert-category">${alert.category}</div>
            </div>
        `).join('');

        $('#alertList').html(alertHtml);
    }

    /**
     * 渲染实时统计
     */
    renderRealTimeStats() {
        if (!this.data.realTimeStats) return;

        const stats = this.data.realTimeStats;
        $('#activeDevicesCount').text(stats.activeDevices);
        $('#totalAlertsCount').text(stats.totalAlerts);
        $('#energyUsageValue').text(stats.energyUsage + ' kWh');
        $('#systemLoadValue').text(stats.systemLoad + '%');
        $('#networkLatencyValue').text(stats.networkLatency + 'ms');
        $('#dataProcessingValue').text(stats.dataProcessingRate + '/s');
        $('#lastUpdateValue').text(stats.lastUpdate);
    }

    /**
     * 显示预警详情
     */
    showAlertDetail(alertId) {
        const alert = this.data.alerts.find(a => a.id === alertId);
        if (!alert) return;

        const content = `
            <div class="alert-detail">
                <h5>${alert.title}</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>预警ID:</label>
                        <span>${alert.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>预警级别:</label>
                        <span class="level ${alert.level}">${this.getAlertLevelText(alert.level)}</span>
                    </div>
                    <div class="detail-item">
                        <label>预警类别:</label>
                        <span>${alert.category}</span>
                    </div>
                    <div class="detail-item">
                        <label>发生时间:</label>
                        <span>${alert.time}</span>
                    </div>
                    <div class="detail-item">
                        <label>状态:</label>
                        <span>${alert.status === 'active' ? '活跃' : '已处理'}</span>
                    </div>
                </div>
                <div class="detail-message">
                    <label>详细信息:</label>
                    <p>${alert.message}</p>
                </div>
            </div>
        `;

        $('#alertDetailContent').html(content);
        this.currentAlertId = alertId;
        $('#alertDetailModal').modal('show');
    }

    /**
     * 自定义KPI
     */
    customizeKPI() {
        // 生成KPI选项
        const availableKPIs = [
            { id: 'device_health', name: '设备完好率', selected: true },
            { id: 'energy_efficiency', name: '能耗效率', selected: true },
            { id: 'fault_response', name: '故障响应时间', selected: true },
            { id: 'warning_accuracy', name: '预警准确率', selected: true },
            { id: 'system_availability', name: '系统可用性', selected: true },
            { id: 'inspection_coverage', name: '巡检覆盖率', selected: true },
            { id: 'maintenance_cost', name: '维护成本', selected: false },
            { id: 'operation_cost', name: '运营成本', selected: false }
        ];

        const optionsHtml = availableKPIs.map(kpi => `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="kpi_${kpi.id}" ${kpi.selected ? 'checked' : ''}>
                <label class="form-check-label" for="kpi_${kpi.id}">${kpi.name}</label>
            </div>
        `).join('');

        $('#kpiOptions').html(optionsHtml);
        $('#kpiCustomModal').modal('show');
    }

    /**
     * 保存KPI自定义设置
     */
    saveKpiCustomization() {
        const selectedKPIs = [];
        $('#kpiOptions input:checked').each((index, element) => {
            const kpiId = element.id.replace('kpi_', '');
            selectedKPIs.push(kpiId);
        });

        console.log('已选择的KPI:', selectedKPIs);
        $('#kpiCustomModal').modal('hide');
        this.showSuccess('KPI设置已保存');
    }

    /**
     * 确认预警
     */
    acknowledgeAlert() {
        if (!this.currentAlertId) return;

        const alert = this.data.alerts.find(a => a.id === this.currentAlertId);
        if (alert) {
            alert.status = 'acknowledged';
        }

        $('#alertDetailModal').modal('hide');
        this.renderAlerts();
        this.showSuccess('预警已确认');
    }

    /**
     * 解决预警
     */
    resolveAlert() {
        if (!this.currentAlertId) return;

        const alert = this.data.alerts.find(a => a.id === this.currentAlertId);
        if (alert) {
            alert.status = 'resolved';
        }

        $('#alertDetailModal').modal('hide');
        this.renderAlerts();
        this.showSuccess('预警已解决');
    }

    /**
     * 刷新仪表板
     */
    async refreshDashboard() {
        try {
            await this.loadData();
            this.renderDashboard();
            this.showSuccess('仪表板已刷新');
        } catch (error) {
            console.error('刷新失败:', error);
            this.showError('刷新失败，请重试');
        }
    }

    /**
     * 导出仪表板
     */
    exportDashboard() {
        const dashboardData = {
            timestamp: new Date().toISOString(),
            timeRange: this.currentTimeRange,
            healthScore: this.data.healthScore,
            kpiMetrics: this.data.kpiMetrics,
            equipmentStatus: this.data.equipmentStatus,
            alerts: this.data.alerts
        };

        const blob = new Blob([JSON.stringify(dashboardData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `仪表板报告_${new Date().toLocaleDateString('zh-CN')}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showSuccess('仪表板数据导出成功');
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(async () => {
            try {
                await this.loadData();
                this.renderRealTimeStats();
                this.renderHealthCockpit();
            } catch (error) {
                console.error('自动刷新失败:', error);
            }
        }, 60000); // 1分钟刷新一次
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 获取趋势图标
     */
    getTrendIcon(trend) {
        const icons = {
            'up': '📈',
            'down': '📉',
            'stable': '➡️'
        };
        return icons[trend] || '';
    }

    /**
     * 获取严重程度类名
     */
    getSeverityClass(severity) {
        const classMap = {
            'high': 'high',
            'medium': 'medium',
            'low': 'low'
        };
        return classMap[severity] || 'medium';
    }

    /**
     * 获取预警级别文本
     */
    getAlertLevelText(level) {
        const levelMap = {
            'critical': '严重',
            'warning': '警告',
            'info': '信息'
        };
        return levelMap[level] || level;
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('成功:', message);
        // 可以使用 toast 或其他通知组件
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('错误:', message);
        // 可以使用 toast 或其他通知组件
    }
}

// 全局函数供HTML调用
window.refreshDashboard = () => visualDashboard.refreshDashboard();
window.exportDashboard = () => visualDashboard.exportDashboard();
window.customizeKPI = () => visualDashboard.customizeKPI();
window.saveKpiCustomization = () => visualDashboard.saveKpiCustomization();
window.acknowledgeAlert = () => visualDashboard.acknowledgeAlert();
window.resolveAlert = () => visualDashboard.resolveAlert();

// 页面加载完成后初始化
let visualDashboard;
$(document).ready(() => {
    visualDashboard = new VisualDashboard();
});

// 页面卸载时清理
$(window).on('beforeunload', () => {
    if (visualDashboard) {
        visualDashboard.stopAutoRefresh();
    }
});