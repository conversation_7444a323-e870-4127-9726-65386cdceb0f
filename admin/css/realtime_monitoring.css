/* ========================================
   多维度实时监控页面样式
   ======================================== */

/* TDesign 颜色变量定义 */
:root {
  /* 品牌色 */
  --td-brand-color: #0052d9;
  --td-brand-color-hover: #266fe8;
  --td-brand-color-active: #0034b5;
  --td-brand-color-light: #e3f2fd;

  /* 功能色 */
  --td-success-color: #00a870;
  --td-success-color-light: #e8f5e8;
  --td-warning-color: #ed7b2f;
  --td-warning-color-light: #fff3e0;
  --td-error-color: #d54941;
  --td-error-color-light: #ffebee;

  /* 文字色 */
  --td-text-color-primary: #000000;
  --td-text-color-secondary: #666666;
  --td-text-color-placeholder: #bbbbbb;
  --td-text-color-anti: #ffffff;

  /* 背景色 */
  --td-bg-color-page: #f5f5f5;
  --td-bg-color-container: #ffffff;
  --td-bg-color-secondarycontainer: #f8f9fa;

  /* 边框色 */
  --td-border-color: #e7e7e7;
  --td-border-color-light: #f0f0f0;

  /* 阴影 */
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05);
  --td-shadow-3: 0 6px 30px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --td-radius-default: 6px;
  --td-radius-medium: 8px;

  /* 间距 */
  --td-comp-size-m: 16px;
  --td-comp-size-l: 20px;
  --td-comp-size-xl: 24px;
  --td-comp-size-xxl: 32px;
}

/* 页面基础样式 */
body {
  background-color: var(--td-bg-color-page);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 页面标题栏 */
.page-header {
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-color);
  padding: var(--td-comp-size-m) var(--td-comp-size-xl);
  margin-bottom: var(--td-comp-size-l);
  box-shadow: var(--td-shadow-1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  color: var(--td-text-color-primary);
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header .logo-icon {
  margin-right: 12px;
  font-size: 28px;
}

.btn-back {
  background: none;
  border: 1px solid var(--td-border-color);
  color: var(--td-text-color-secondary);
  padding: 8px 16px;
  border-radius: var(--td-radius-default);
  margin-right: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-back:hover {
  background-color: var(--td-bg-color-secondarycontainer);
  border-color: var(--td-brand-color);
  color: var(--td-brand-color);
}

.real-time-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--td-text-color-secondary);
  font-size: 14px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--td-error-color);
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background-color: var(--td-success-color);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 统计概览卡片 */
.stats-overview {
  margin-bottom: var(--td-comp-size-xl);
}

.stat-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  padding: var(--td-comp-size-l);
  box-shadow: var(--td-shadow-1);
  border: 1px solid var(--td-border-color-light);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  height: 120px;
}

.stat-card:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--td-radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.devices {
  background: linear-gradient(135deg, var(--td-brand-color), var(--td-brand-color-hover));
}

.stat-icon.personnel {
  background: linear-gradient(135deg, var(--td-success-color), #00c78c);
}

.stat-icon.alarms {
  background: linear-gradient(135deg, var(--td-error-color), #ff6b6b);
}

.stat-icon.health {
  background: linear-gradient(135deg, var(--td-warning-color), #ffab40);
}

.stat-info h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--td-text-color-primary);
}

.stat-info p {
  margin: 4px 0 8px 0;
  color: var(--td-text-color-secondary);
  font-size: 14px;
}

.stat-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-details span {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
}

.stat-details .normal {
  background: var(--td-success-color-light);
  color: var(--td-success-color);
}

.stat-details .warning {
  background: var(--td-warning-color-light);
  color: var(--td-warning-color);
}

.stat-details .error {
  background: var(--td-error-color-light);
  color: var(--td-error-color);
}

.stat-details .critical {
  background: var(--td-error-color-light);
  color: var(--td-error-color);
}

.stat-details .total {
  background: var(--td-bg-color-secondarycontainer);
  color: var(--td-text-color-secondary);
}

.stat-details .efficiency {
  background: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

/* 监控面板样式 */
.monitor-panel {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-1);
  border: 1px solid var(--td-border-color-light);
  margin-bottom: var(--td-comp-size-l);
  overflow: hidden;
}

.panel-header {
  background: var(--td-bg-color-secondarycontainer);
  padding: var(--td-comp-size-m) var(--td-comp-size-l);
  border-bottom: 1px solid var(--td-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: var(--td-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.panel-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.panel-controls .form-select {
  border: 1px solid var(--td-border-color);
  border-radius: var(--td-radius-default);
  padding: 6px 12px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.btn-refresh {
  background: var(--td-brand-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: var(--td-radius-default);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-refresh:hover {
  background: var(--td-brand-color-hover);
}

.panel-body {
  padding: var(--td-comp-size-l);
  max-height: 400px;
  overflow-y: auto;
}

/* 设备列表样式 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-item {
  background: var(--td-bg-color-secondarycontainer);
  border: 1px solid var(--td-border-color-light);
  border-radius: var(--td-radius-default);
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.device-item:hover {
  box-shadow: var(--td-shadow-1);
  border-color: var(--td-brand-color);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-weight: 600;
  color: var(--td-text-color-primary);
  font-size: 14px;
}

.device-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.device-status.normal {
  background: var(--td-success-color-light);
  color: var(--td-success-color);
}

.device-status.warning {
  background: var(--td-warning-color-light);
  color: var(--td-warning-color);
}

.device-status.error {
  background: var(--td-error-color-light);
  color: var(--td-error-color);
}

.device-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 13px;
}

.device-info .info-item {
  display: flex;
  justify-content: space-between;
}

.device-info .info-label {
  color: var(--td-text-color-secondary);
}

.device-info .info-value {
  color: var(--td-text-color-primary);
  font-weight: 500;
}

/* 环境参数网格 */
.environment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.environment-card {
  background: var(--td-bg-color-secondarycontainer);
  border: 1px solid var(--td-border-color-light);
  border-radius: var(--td-radius-default);
  padding: 16px;
  transition: all 0.3s ease;
}

.environment-card:hover {
  box-shadow: var(--td-shadow-1);
}

.environment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.environment-area {
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.environment-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.environment-status.normal {
  background: var(--td-success-color);
}

.environment-status.warning {
  background: var(--td-warning-color);
}

.environment-status.error {
  background: var(--td-error-color);
}

.environment-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 13px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
}

.metric-label {
  color: var(--td-text-color-secondary);
}

.metric-value {
  color: var(--td-text-color-primary);
  font-weight: 500;
}

/* 人员动线样式 */
.personnel-map {
  margin-bottom: 16px;
}

.map-container {
  background: var(--td-bg-color-secondarycontainer);
  border: 1px solid var(--td-border-color);
  border-radius: var(--td-radius-default);
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--td-text-color-secondary);
  position: relative;
  overflow: hidden;
}

.area-marker {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--td-brand-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.area-marker:hover {
  transform: scale(1.1);
  box-shadow: var(--td-shadow-2);
}

.area-marker.has-personnel {
  background: var(--td-success-color);
  animation: pulse 2s infinite;
}

.personnel-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.personnel-item {
  background: var(--td-bg-color-secondarycontainer);
  border: 1px solid var(--td-border-color-light);
  border-radius: var(--td-radius-default);
  padding: 12px;
}

.personnel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.personnel-name {
  font-weight: 600;
  color: var(--td-text-color-primary);
  font-size: 14px;
}

.personnel-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--td-success-color);
}

.personnel-info {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.personnel-info div {
  margin-bottom: 4px;
}

/* 报警列表样式 */
.alarm-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alarm-item {
  border-left: 4px solid;
  background: var(--td-bg-color-secondarycontainer);
  padding: 12px;
  border-radius: 0 var(--td-radius-default) var(--td-radius-default) 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alarm-item:hover {
  box-shadow: var(--td-shadow-1);
}

.alarm-item.critical {
  border-left-color: var(--td-error-color);
}

.alarm-item.warning {
  border-left-color: var(--td-warning-color);
}

.alarm-item.info {
  border-left-color: var(--td-brand-color);
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alarm-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.alarm-level.critical {
  background: var(--td-error-color-light);
  color: var(--td-error-color);
}

.alarm-level.warning {
  background: var(--td-warning-color-light);
  color: var(--td-warning-color);
}

.alarm-level.info {
  background: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

.alarm-time {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.alarm-message {
  font-size: 13px;
  color: var(--td-text-color-primary);
  margin-bottom: 4px;
}

.alarm-location {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

/* 图表容器样式 */
.chart-panels {
  margin-top: var(--td-comp-size-l);
}

#realtimeChart {
  width: 100% !important;
  height: 300px !important;
}

/* 模态框样式调整 */
.modal-content {
  border-radius: var(--td-radius-medium);
  border: none;
  box-shadow: var(--td-shadow-3);
}

.modal-header {
  background: var(--td-bg-color-secondarycontainer);
  border-bottom: 1px solid var(--td-border-color);
  border-radius: var(--td-radius-medium) var(--td-radius-medium) 0 0;
}

.modal-title {
  color: var(--td-text-color-primary);
  font-weight: 600;
}

.btn-close {
  filter: none;
  opacity: 0.6;
}

.btn-close:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .dashboard-controls {
    flex-wrap: wrap;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    height: auto;
    padding: 16px;
  }

  .panel-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .environment-grid {
    grid-template-columns: 1fr;
  }

  .personnel-list {
    grid-template-columns: 1fr;
  }
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--td-text-color-secondary);
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--td-border-color);
  border-top: 2px solid var(--td-brand-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}