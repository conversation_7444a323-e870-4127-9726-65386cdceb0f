/* ========================================
   可视化看板与预警页面样式
   ======================================== */

/* TDesign 颜色变量定义 */
:root {
    /* 品牌色 */
    --td-brand-color: #0052d9;
    --td-brand-color-hover: #266fe8;
    --td-brand-color-active: #0034b5;
    --td-brand-color-light: #e3f2fd;

    /* 功能色 */
    --td-success-color: #00a870;
    --td-success-color-light: #e8f5e8;
    --td-warning-color: #ed7b2f;
    --td-warning-color-light: #fff3e0;
    --td-error-color: #d54941;
    --td-error-color-light: #ffebee;

    /* 文字色 */
    --td-text-color-primary: #000000;
    --td-text-color-secondary: #666666;
    --td-text-color-placeholder: #bbbbbb;
    --td-text-color-anti: #ffffff;

    /* 背景色 */
    --td-bg-color-page: #f5f5f5;
    --td-bg-color-container: #ffffff;
    --td-bg-color-secondarycontainer: #f8f9fa;

    /* 边框色 */
    --td-border-color: #e7e7e7;
    --td-border-color-light: #f0f0f0;

    /* 阴影 */
    --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05);
    --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05);
    --td-shadow-3: 0 6px 30px rgba(0, 0, 0, 0.12);

    /* 圆角 */
    --td-radius-default: 6px;
    --td-radius-medium: 8px;

    /* 间距 */
    --td-comp-size-m: 16px;
    --td-comp-size-l: 20px;
    --td-comp-size-xl: 24px;
    --td-comp-size-xxl: 32px;
  }

  /* 页面基础样式 */
  body {
    background-color: var(--td-bg-color-page);
    font-family: 'Microsoft YaHei', Arial, sans-serif;
  }

  /* 页面标题栏 */
  .page-header {
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-color);
    padding: var(--td-comp-size-m) var(--td-comp-size-xl);
    margin-bottom: var(--td-comp-size-l);
    box-shadow: var(--td-shadow-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .page-header h1 {
    color: var(--td-text-color-primary);
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }

  .page-header .logo-icon {
    margin-right: 12px;
    font-size: 28px;
  }

  .btn-back {
    background: none;
    border: 1px solid var(--td-border-color);
    color: var(--td-text-color-secondary);
    padding: 8px 16px;
    border-radius: var(--td-radius-default);
    margin-right: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-back:hover {
    background-color: var(--td-bg-color-secondarycontainer);
    border-color: var(--td-brand-color);
    color: var(--td-brand-color);
  }

  .dashboard-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .dashboard-controls .form-select {
    border: 1px solid var(--td-border-color);
    border-radius: var(--td-radius-default);
    padding: 8px 12px;
    font-size: 14px;
    background: white;
    min-width: 120px;
  }

  .btn-refresh, .btn-export {
    background: var(--td-brand-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--td-radius-default);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .btn-refresh:hover, .btn-export:hover {
    background: var(--td-brand-color-hover);
  }

  .btn-export {
    background: var(--td-success-color);
  }

  .btn-export:hover {
    background: #00c78c;
  }

  /* 仪表板面板通用样式 */
  .dashboard-panel {
    background: var(--td-bg-color-container);
    border-radius: var(--td-radius-medium);
    box-shadow: var(--td-shadow-1);
    border: 1px solid var(--td-border-color-light);
    margin-bottom: var(--td-comp-size-l);
    overflow: hidden;
  }

  .panel-header {
    background: var(--td-bg-color-secondarycontainer);
    padding: var(--td-comp-size-m) var(--td-comp-size-l);
    border-bottom: 1px solid var(--td-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .panel-header h3 {
    margin: 0;
    color: var(--td-text-color-primary);
    font-size: 16px;
    font-weight: 600;
  }

  .panel-controls {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .panel-controls .form-select {
    border: 1px solid var(--td-border-color);
    border-radius: var(--td-radius-default);
    padding: 6px 12px;
    font-size: 14px;
    background: white;
    min-width: 120px;
  }

  .panel-body {
    padding: var(--td-comp-size-l);
  }

  /* 运营健康度驾驶舱 */
  .health-cockpit {
    margin-bottom: var(--td-comp-size-xl);
  }

  .health-score-display {
    position: absolute;
    top: 16px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .score-circle {
    position: relative;
    width: 120px;
    height: 120px;
  }

  .score-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .score-text span {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: var(--td-brand-color);
  }

  .score-text small {
    font-size: 12px;
    color: var(--td-text-color-secondary);
  }

  .health-categories {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 20px;
  }

  .category-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: var(--td-radius-default);
    border: 1px solid var(--td-border-color-light);
  }

  .category-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
  }

  .category-info {
    flex: 1;
  }

  .category-info h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: var(--td-text-color-primary);
    font-weight: 600;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--td-border-color-light);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
  }

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-fill.equipment {
    background: linear-gradient(90deg, var(--td-brand-color), var(--td-brand-color-hover));
  }

  .progress-fill.environment {
    background: linear-gradient(90deg, var(--td-success-color), #00c78c);
  }

  .progress-fill.personnel {
    background: linear-gradient(90deg, #9c27b0, #ba68c8);
  }

  .progress-fill.safety {
    background: linear-gradient(90deg, var(--td-warning-color), #ffab40);
  }

  .progress-fill.efficiency {
    background: linear-gradient(90deg, #607d8b, #90a4ae);
  }

  .category-item .score {
    font-weight: 600;
    color: var(--td-text-color-primary);
    font-size: 16px;
  }

  /* KPI指标网格 */
  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .kpi-card {
    background: var(--td-bg-color-secondarycontainer);
    border: 1px solid var(--td-border-color-light);
    border-radius: var(--td-radius-default);
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .kpi-card:hover {
    box-shadow: var(--td-shadow-1);
    transform: translateY(-2px);
  }

  .kpi-name {
    font-size: 14px;
    color: var(--td-text-color-secondary);
    margin-bottom: 8px;
  }

  .kpi-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--td-text-color-primary);
    margin-bottom: 4px;
  }

  .kpi-unit {
    font-size: 14px;
    color: var(--td-text-color-secondary);
  }

  .kpi-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-top: 8px;
    font-size: 12px;
  }

  .kpi-trend.up {
    color: var(--td-success-color);
  }

  .kpi-trend.down {
    color: var(--td-error-color);
  }

  .kpi-trend.stable {
    color: var(--td-text-color-secondary);
  }

  .kpi-target {
    font-size: 12px;
    color: var(--td-text-color-placeholder);
    margin-top: 4px;
  }

  /* 设备状态分布 */
  .equipment-overview {
    display: flex;
    gap: 24px;
    align-items: center;
    margin-bottom: 20px;
  }

  .equipment-chart {
    flex-shrink: 0;
  }

  .equipment-stats {
    flex: 1;
  }

  .stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--td-border-color-light);
  }

  .stat-row:last-child {
    border-bottom: none;
  }

  .stat-label {
    font-size: 14px;
    color: var(--td-text-color-secondary);
  }

  .stat-value {
    font-weight: 600;
    color: var(--td-text-color-primary);
  }

  .equipment-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
  }

  .category-card {
    background: var(--td-bg-color-secondarycontainer);
    border: 1px solid var(--td-border-color-light);
    border-radius: var(--td-radius-default);
    padding: 12px;
  }

  .category-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 8px;
  }

  .category-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
  }

  .category-stats span {
    padding: 2px 6px;
    border-radius: 8px;
  }

  .category-stats .normal {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
  }

  .category-stats .warning {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
  }

  .category-stats .error {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
  }

  /* 故障预测列表 */
  .prediction-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 350px;
    overflow-y: auto;
  }

  .prediction-item {
    background: var(--td-bg-color-secondarycontainer);
    border-radius: var(--td-radius-default);
    padding: 16px;
    border-left: 4px solid;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .prediction-item:hover {
    box-shadow: var(--td-shadow-1);
  }

  .prediction-item.high {
    border-left-color: var(--td-error-color);
  }

  .prediction-item.medium {
    border-left-color: var(--td-warning-color);
  }

  .prediction-item.low {
    border-left-color: var(--td-success-color);
  }

  .prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .prediction-device {
    font-weight: 600;
    color: var(--td-text-color-primary);
    font-size: 14px;
  }

  .prediction-probability {
    font-size: 16px;
    font-weight: 700;
  }

  .prediction-probability.high {
    color: var(--td-error-color);
  }

  .prediction-probability.medium {
    color: var(--td-warning-color);
  }

  .prediction-probability.low {
    color: var(--td-success-color);
  }

  .prediction-details {
    font-size: 12px;
    color: var(--td-text-color-secondary);
  }

  .prediction-fault {
    margin-bottom: 4px;
  }

  .prediction-time {
    color: var(--td-warning-color);
  }

  .prediction-advice {
    font-size: 12px;
    color: var(--td-text-color-primary);
    background: var(--td-brand-color-light);
    padding: 8px;
    border-radius: var(--td-radius-default);
    margin-top: 8px;
  }

  /* 运营数据面板 */
  .energy-overview, .efficiency-overview, .throughput-overview {
    text-align: center;
    margin-bottom: 16px;
  }

  .energy-current h2, .efficiency-current h2, .throughput-current h2 {
    margin: 0;
    font-size: 36px;
    font-weight: 700;
    color: var(--td-brand-color);
  }

  .energy-current span, .efficiency-current span, .throughput-current span {
    font-size: 18px;
    color: var(--td-text-color-secondary);
    margin-left: 4px;
  }

  .energy-current small, .efficiency-current small, .throughput-current small {
    display: block;
    font-size: 14px;
    color: var(--td-text-color-secondary);
    margin-top: 4px;
  }

  .energy-comparison, .efficiency-target {
    margin-top: 8px;
    font-size: 14px;
  }

  .comparison-label {
    color: var(--td-text-color-secondary);
  }

  .comparison-value {
    font-weight: 600;
    margin-left: 8px;
  }

  .comparison-value.positive {
    color: var(--td-success-color);
  }

  .comparison-value.negative {
    color: var(--td-error-color);
  }

  .efficiency-areas {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
  }

  .area-efficiency {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: var(--td-radius-default);
    font-size: 13px;
  }

  .area-name {
    color: var(--td-text-color-secondary);
  }

  .area-value {
    font-weight: 600;
    color: var(--td-text-color-primary);
  }

  .throughput-progress {
    margin-top: 12px;
  }

  .throughput-progress .progress-bar {
    height: 12px;
    margin-bottom: 8px;
  }

  .throughput-progress .progress-fill {
    background: linear-gradient(90deg, var(--td-success-color), #00c78c);
  }

  /* 温度热力图 */
  .temperature-heatmap {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 16px;
  }

  .heatmap-cell {
    aspect-ratio: 1;
    border-radius: var(--td-radius-default);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .heatmap-cell:hover {
    transform: scale(1.05);
    box-shadow: var(--td-shadow-2);
  }

  .heatmap-cell.normal {
    background: linear-gradient(135deg, var(--td-success-color), #00c78c);
  }

  .heatmap-cell.warning {
    background: linear-gradient(135deg, var(--td-warning-color), #ffab40);
  }

  .heatmap-cell.error {
    background: linear-gradient(135deg, var(--td-error-color), #ff6b6b);
  }

  .cell-area {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .cell-temp {
    font-size: 14px;
    font-weight: 700;
  }

  .temperature-legend {
    display: flex;
    justify-content: space-around;
    font-size: 12px;
  }

  .legend-item {
    padding: 4px 8px;
    border-radius: 12px;
  }

  .legend-item.normal {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
  }

  .legend-item.warning {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
  }

  .legend-item.error {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
  }

  /* 实时预警面板 */
  .alert-summary {
    display: flex;
    gap: 8px;
  }

  .alert-count {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
  }

  .alert-count.critical {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
  }

  .alert-count.warning {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
  }

  .alert-count.info {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
  }

  .alert-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 350px;
    overflow-y: auto;
  }

  .alert-item {
    background: var(--td-bg-color-secondarycontainer);
    border-radius: var(--td-radius-default);
    padding: 12px;
    border-left: 4px solid;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .alert-item:hover {
    box-shadow: var(--td-shadow-1);
  }

  .alert-item.critical {
    border-left-color: var(--td-error-color);
  }

  .alert-item.warning {
    border-left-color: var(--td-warning-color);
  }

  .alert-item.info {
    border-left-color: var(--td-brand-color);
  }

  .alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .alert-title {
    font-weight: 600;
    color: var(--td-text-color-primary);
    font-size: 14px;
  }

  .alert-time {
    font-size: 12px;
    color: var(--td-text-color-secondary);
  }

  .alert-message {
    font-size: 13px;
    color: var(--td-text-color-primary);
    margin-bottom: 4px;
  }

  .alert-category {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    background: var(--td-bg-color-container);
    padding: 2px 6px;
    border-radius: 8px;
    display: inline-block;
  }

  /* 实时统计信息底部面板 */
  .stats-panel .panel-body {
    padding: 16px 24px;
  }

  .real-time-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 24px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-label {
    display: block;
    font-size: 12px;
    color: var(--td-text-color-secondary);
    margin-bottom: 4px;
  }

  .stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
  }

  /* 图表容器样式 */
  canvas {
    max-width: 100%;
    height: auto;
  }

  #healthTrendChart {
    width: 100% !important;
    height: 200px !important;
  }

  #equipmentStatusChart {
    width: 250px !important;
    height: 250px !important;
  }

  #energyTrendChart, #throughputChart {
    width: 100% !important;
    height: 150px !important;
  }

  /* 模态框样式调整 */
  .modal-content {
    border-radius: var(--td-radius-medium);
    border: none;
    box-shadow: var(--td-shadow-3);
  }

  .modal-header {
    background: var(--td-bg-color-secondarycontainer);
    border-bottom: 1px solid var(--td-border-color);
    border-radius: var(--td-radius-medium) var(--td-radius-medium) 0 0;
  }

  .modal-title {
    color: var(--td-text-color-primary);
    font-weight: 600;
  }

  .btn-close {
    filter: none;
    opacity: 0.6;
  }

  .btn-close:hover {
    opacity: 1;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .health-categories {
      grid-template-columns: 1fr;
    }

    .kpi-grid {
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .equipment-overview {
      flex-direction: column;
      text-align: center;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    .dashboard-controls {
      flex-wrap: wrap;
    }

    .panel-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    .kpi-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .equipment-categories {
      grid-template-columns: 1fr;
    }

    .temperature-heatmap {
      grid-template-columns: repeat(2, 1fr);
    }

    .real-time-stats {
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
    }

    .health-score-display {
      position: static;
      justify-content: center;
      margin-bottom: 16px;
    }
  }

  /* 加载动画 */
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--td-text-color-secondary);
  }

  .loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--td-border-color);
    border-top: 2px solid var(--td-brand-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }