/* ========================================
   多终端协同操作页面样式
   ======================================== */

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: var(--td-shadow-1);
}

.toolbar-left {
    display: flex;
    gap: 15px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 6px;
    border: 1px solid var(--td-border-color);
    overflow: hidden;
}

.search-input {
    border: none;
    outline: none;
    padding: 8px 12px;
    background: transparent;
    font-size: 14px;
    width: 280px;
    color: var(--td-text-color-primary);
}

.search-input::placeholder {
    color: var(--td-text-color-placeholder);
}

.search-btn {
    background: var(--td-brand-color);
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background: var(--td-brand-color-hover);
}

/* 筛选框样式 */
.filter-box {
    display: flex;
    gap: 10px;
}

.filter-box select {
    padding: 8px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

.filter-box select:focus {
    border-color: var(--td-brand-color);
}

/* 终端卡片网格 */
.terminal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 终端卡片样式 */
.terminal-card {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--td-shadow-1);
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;
}

.terminal-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--td-shadow-2);
}

.terminal-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--td-border-color-light);
}

.terminal-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.terminal-icon {
    font-size: 18px;
}

.terminal-actions {
    display: flex;
    gap: 8px;
}

.terminal-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 14px;
}

.terminal-action-btn:hover {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.terminal-action-btn.active {
    background: var(--td-brand-color);
    color: white;
}

.terminal-body {
    padding: 16px;
}

.terminal-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.terminal-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.terminal-info-label {
    color: var(--td-text-color-secondary);
}

.terminal-info-value {
    color: var(--td-text-color-primary);
    font-weight: 500;
}

.terminal-footer {
    padding: 12px 16px;
    background: var(--td-bg-color-secondarycontainer);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 电池指示器 */
.battery-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.battery-icon {
    width: 24px;
    height: 12px;
    border: 1px solid var(--td-text-color-primary);
    border-radius: 2px;
    position: relative;
    margin-right: 2px;
}

.battery-icon:after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: var(--td-text-color-primary);
    border-radius: 0 1px 1px 0;
}

.battery-level {
    position: absolute;
    left: 1px;
    top: 1px;
    bottom: 1px;
    border-radius: 1px;
    transition: width 0.3s;
}

.battery-level.high {
    background: var(--td-success-color);
}

.battery-level.medium {
    background: var(--td-warning-color);
}

.battery-level.low {
    background: var(--td-error-color);
}

.battery-text {
    font-size: 12px;
    font-weight: 500;
}

/* 同步状态 */
.sync-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
}

.sync-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sync-icon.synced {
    color: var(--td-success-color);
}

.sync-icon.syncing {
    color: var(--td-warning-color);
    animation: rotate 1.5s linear infinite;
}

.sync-icon.error {
    color: var(--td-error-color);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 设备类型标识 */
.device-type {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.device-type.handheld {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.device-type.tablet {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

.device-type.kiosk {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

.device-type.desktop {
    background: var(--td-info-color-light);
    color: var(--td-info-color);
}

/* 在线状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
}

.status-indicator.online {
    background: var(--td-success-color);
    box-shadow: 0 0 0 2px var(--td-success-color-light);
}

.status-indicator.offline {
    background: var(--td-text-color-placeholder);
}

.status-indicator.idle {
    background: var(--td-warning-color);
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--td-shadow-1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--td-shadow-2);
}

.stat-icon {
    font-size: 32px;
    padding: 12px;
    border-radius: 50%;
    background: var(--td-brand-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 56px;
    height: 56px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--td-text-color-primary);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--td-text-color-secondary);
    font-weight: 500;
}

/* 终端屏幕预览 */
.terminal-screen {
    width: 100%;
    height: 160px;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    margin-top: 12px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.screen-placeholder {
    color: var(--td-text-color-placeholder);
    font-size: 14px;
    text-align: center;
}

.screen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 终端控制按钮 */
.terminal-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.control-btn {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.control-btn-primary {
    background: var(--td-brand-color);
    color: white;
}

.control-btn-primary:hover {
    background: var(--td-brand-color-hover);
}

.control-btn-secondary {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
}

.control-btn-secondary:hover {
    background: var(--td-border-color-light);
}

/* 终端日志 */
.terminal-logs {
    margin-top: 16px;
    max-height: 100px;
    overflow-y: auto;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 4px;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    color: var(--td-text-color-secondary);
}

.log-entry {
    padding: 2px 0;
    border-bottom: 1px dashed var(--td-border-color-light);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: var(--td-warning-color);
    margin-right: 8px;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    box-shadow: var(--td-shadow-1);
}

.pagination-info {
    color: var(--td-text-color-secondary);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    min-width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid var(--td-border-color);
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    border-color: var(--td-brand-color);
    color: var(--td-brand-color);
}

.pagination-btn.active {
    background: var(--td-brand-color);
    color: white;
    border-color: var(--td-brand-color);
}

.pagination-btn:disabled {
    cursor: not-allowed;
    color: var(--td-text-color-placeholder);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--td-shadow-3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--td-border-color);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 20px;
    color: var(--td-text-color-secondary);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background: var(--td-bg-color-secondarycontainer);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--td-border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--td-text-color-primary);
    background: var(--td-bg-color-container);
    transition: border-color 0.2s;
}

.form-control:focus {
    border-color: var(--td-brand-color);
    outline: none;
}

.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

/* 远程控制界面 */
.remote-control-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.remote-screen {
    width: 100%;
    height: 300px;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remote-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.remote-btn {
    padding: 10px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.remote-btn-primary {
    background: var(--td-brand-color);
    color: white;
}

.remote-btn-primary:hover {
    background: var(--td-brand-color-hover);
}

.remote-btn-secondary {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
}

.remote-btn-secondary:hover {
    background: var(--td-border-color-light);
}

.remote-btn-danger {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

.remote-btn-danger:hover {
    background: var(--td-error-color);
    color: white;
}

/* 数据表格样式 */
.data-table-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--td-shadow-1);
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--td-border-color-light);
}

.data-table th {
    background: var(--td-bg-color-secondarycontainer);
    font-weight: 600;
    color: var(--td-text-color-primary);
    font-size: 14px;
}

.data-table td {
    color: var(--td-text-color-primary);
    font-size: 14px;
}

.data-table tr:hover {
    background: var(--td-bg-color-secondarycontainer);
}

/* 设备类型徽章 */
.device-type-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.device-type-badge.pda {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.device-type-badge.ar,
.device-type-badge.ar眼镜 {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

.device-type-badge.平板电脑 {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

/* 状态徽章 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-badge.online {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

.status-badge.offline {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

.status-badge.maintenance {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary {
    background: var(--td-brand-color);
    color: white;
}

.btn-primary:hover {
    background: var(--td-brand-color-hover);
}

.btn-warning {
    background: var(--td-warning-color);
    color: white;
}

.btn-warning:hover {
    background: var(--td-warning-color-hover);
}

.btn-info {
    background: var(--td-info-color);
    color: white;
}

.btn-info:hover {
    background: var(--td-info-color-hover);
}

.btn-secondary {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
    border: 1px solid var(--td-border-color);
}

.btn-secondary:hover {
    background: var(--td-border-color-light);
}

/* 终端详情样式 */
.terminal-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.detail-section {
    background: var(--td-bg-color-secondarycontainer);
    padding: 16px;
    border-radius: 6px;
}

.detail-section h4 {
    margin: 0 0 16px 0;
    color: var(--td-text-color-primary);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid var(--td-border-color-light);
    padding-bottom: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px dashed var(--td-border-color-light);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 500;
    color: var(--td-text-color-secondary);
    min-width: 100px;
}

.detail-item span {
    color: var(--td-text-color-primary);
    text-align: right;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--td-text-color-placeholder);
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state-text {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
}

.empty-state-desc {
    font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .toolbar-left {
        flex-direction: column;
    }

    .search-input {
        width: 100%;
    }

    .terminal-grid {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 16px;
    }

    .terminal-detail-grid {
        grid-template-columns: 1fr;
    }

    .data-table-container {
        overflow-x: auto;
    }

    .action-buttons {
        flex-direction: column;
    }
}
